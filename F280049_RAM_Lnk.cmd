MEMORY
{
/*
    ClaProgPos = 0
    CpuDataPos = 0
    ClaDataSz = 0X0
    ClaProgStart = 0x8000
    ClaProgSz = 0X0
    CpuDataStart = 0x8000
    CpuDataSz = 0X4000
    CpuProgStart = 0xC000
    CpuProgSz = 0x7FF8
*/
PAGE 0 :  /* Program space */
   /* BEGIN is used for the "boot to SARAM" bootloader mode   */
   BEGIN        : origin = 0x000000, length = 0x000002
   RAMF         : origin = 0x000200, length = 0x000200  /* on-chip RAM block M0 */
 
   RESET        : origin = 0x3FFFC0, length = 0x000002
   RAMPCPU      : origin = 0xC000, length = 0x7FF8	/* All local share memory and RAMGS0 */
PAGE 1 :      /* Data space */
   BOOT_RSVD    : origin = 0x000002, length = 0x0000F3  /* Part of M0, BOOT rom will use this for stack */
   RAMM0        : origin = 0x0000F5, length = 0x00030B	/* Use for stack */
   RAMM1        : origin = 0x000400, length = 0x0003F8  /* on-chip RAM block M1 */
   RAMDCPU      : origin = 0x8000, length = 0X4000	/* All local share memory and RAMGS0 */
   MSG_CLAtoCPU : origin = 0x001480, length = 0x000080
   MSG_CPUtoCLA : origin = 0x001500, length = 0x000080
}
SECTIONS
{
   codestart        : > BEGIN,                PAGE = 0
   .reset           : > RESET,                PAGE = 0, TYPE = DSECT /* not used, */
   .text            : > RAMPCPU,              PAGE = 0
   .stack           : > RAMM0,                PAGE = 1
   .ebss            : >> RAMM1 | RAMDCPU,     PAGE = 1
   .pinit           : >> RAMM1 | RAMDCPU,     PAGE = 1
   .switch          : >> RAMM1 | RAMDCPU,     PAGE = 1
   .cinit           : > RAMDCPU,              PAGE = 1
   .econst          : > RAMDCPU,              PAGE = 1
   .esysmem         : > RAMDCPU,              PAGE = 1
   .cio		        : > RAMDCPU,              PAGE = 1
}
/*
//===========================================================================
// End of file.
//===========================================================================
*/