MEMORY
{
/*
    ClaProgPos = 0
    CpuDataPos = 0
    ClaDataSz = 0X0
    ClaProgStart = 0x8000
    ClaProgSz = 0X0
    CpuDataStart = 0x8000
    CpuDataSz = 0X4000
    CpuProgStart = 0xC000
    CpuProgSz = 0x7FF8
*/
PAGE 0 :  /* Program space */
   /* BEGIN is used for the "boot to SARAM" bootloader mode   */
   BEGIN        : origin = 0x080000, length = 0x000002
   RAMF         : origin = 0x000200, length = 0x000200  /* on-chip RAM block M0 */
 
   DUMMYPROG    : origin = 0x01F000, length = 0x01000     /* this area does not exist*/
   RAMPCPU      : origin = 0xC000, length = 0x7FF8	/* All local share memory and RAMGS0 */
   RESET        : origin = 0x3FFFC0, length = 0x000002
   FLASHPROG    : origin = 0x080002, length = 0X8002
PAGE 1 :  /* Data space */
   BOOT_RSVD    : origin = 0x000002, length = 0x0000F3  /* Part of M0, BOOT rom will use this for stack */
   RAMM0        : origin = 0x0000F5, length = 0x00030B	/* Use for stack */
   RAMM1        : origin = 0x000400, length = 0x0003F8  /* on-chip RAM block M1 */
   
   DUMMYDATA    : origin = 0x01F000, length = 0x01000     /* this area does not exist*/
   RAMDCPU      : origin = 0x8000, length = 0X4000	/* All local share memory and RAMGS0 */
   MSG_CLAtoCPU : origin = 0x001480, length = 0x000080
   MSG_CPUtoCLA : origin = 0x001500, length = 0x000080
   FLASHDATA    : origin = 0X88004, length = 0x010000
}
SECTIONS
{
   /* Allocate program areas: */
   codestart    : > BEGIN        PAGE = 0
   wddisable	: > FLASHPROG,   PAGE = 0
   copysections : > FLASHPROG,   PAGE = 0
   .cinit       : LOAD = FLASHDATA,
                         RUN = RAMDCPU,
                         LOAD_START(_CinitLoadStart),
                         LOAD_SIZE(_CinitSize),
                         RUN_START(_CinitRunStart),
                         PAGE = 1
   .pinit       : LOAD = FLASHDATA,
                         RUN = RAMDCPU,
                         LOAD_START(_PinitLoadStart),
                         LOAD_SIZE(_PinitSize),
                         RUN_START(_PinitRunStart),
                         PAGE = 1
   .switch      : LOAD = FLASHDATA,
                         RUN = RAMDCPU,
                         LOAD_START(_SwitchLoadStart),
                         LOAD_SIZE(_SwitchSize),
                         RUN_START(_SwitchRunStart),
                         PAGE = 1
   .econst      : LOAD = FLASHDATA,
                         RUN = RAMDCPU,
                         LOAD_START(_EconstLoadStart),
                         LOAD_SIZE(_EconstSize),
                         RUN_START(_EconstRunStart),
                         PAGE = 1
   .text        : LOAD = FLASHPROG,
                         RUN = RAMPCPU,
                         LOAD_START(_TextLoadStart),
                         LOAD_SIZE(_TextSize),
                         RUN_START(_TextRunStart),
                         PAGE = 0
   /* Allocate uninitalized data sections: */
   .stack       : > RAMM0,              PAGE = 1
   .esysmem     : > RAMM1,              PAGE = 1
   .ebss        : >>RAMM1 | RAMDCPU,    PAGE = 1
   .cio		    : >>RAMM1 | RAMDCPU,    PAGE = 1
   .reset       : > RESET,    PAGE = 0, TYPE = DSECT /* not used, */
   ramfuncs     :   LOAD = FLASHPROG,
                         RUN = RAMF,
                         LOAD_START(_RamfuncsLoadStart),
                         LOAD_SIZE(_RamfuncsSize),
                         RUN_START(_RamfuncsRunStart),
                         PAGE = 0  
   Cla1Prog         : LOAD = FLASHPROG,
                         RUN = DUMMYPROG,
                         LOAD_START(_Cla1ProgLoadStart),
                         LOAD_SIZE(_Cla1ProgLoadSize),
                         RUN_START(_Cla1ProgRunStart),
                         PAGE = 0
   .const_cla       : LOAD = FLASHDATA,
                         RUN = DUMMYDATA,
                         LOAD_START(_Cla1ConstLoadStart),
                         LOAD_SIZE(_Cla1ConstLoadSize),
                         RUN_START(_Cla1ConstRunStart),
                         PAGE = 1
}
/*
//===========================================================================
// End of file.
//===========================================================================
*/