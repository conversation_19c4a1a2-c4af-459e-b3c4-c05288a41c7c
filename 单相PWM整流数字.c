/*********************************************************************************
// This code is created by SimCoder Version 2025a15.147 for F28004x Hardware Target
//
// SimCoder is copyright by Powersim Inc., 2009-2021
//
// Date: July 28, 2025 09:53:41
**********************************************************************************/
#include	<math.h>
#include	"PS_bios.h"
#define	GetCurTime() PS_GetSysTimer()
#define	iif(a, b, c) ((a) ? (b) : (c))
typedef interrupt void (*ClaIntr)(void);
ClaIntr Cla1Task1 = 0;
ClaIntr Cla1Task2 = 0;
ClaIntr Cla1Task3 = 0;
ClaIntr Cla1Task4 = 0;
ClaIntr Cla1Task5 = 0;
ClaIntr Cla1Task6 = 0;
ClaIntr Cla1Task7 = 0;
ClaIntr Cla1Task8 = 0;




interrupt void Task();

#ifdef _FLASH
#pragma DATA_SECTION(PSK_SysClk, "copysections")
#endif
const Uint16 PSK_SysClk = 100;  // MHz
extern	DefaultType	fGblV16;
extern	DefaultType	fGblV13;
extern	DefaultType	fGblVma;
extern	DefaultType	fGblV6;
extern	DefaultType	fGblV7;
extern	DefaultType	fGblV8;
extern	DefaultType	fGblV11;
extern	DefaultType	fGblV12;
extern	DefaultType	fGblV14;
extern	DefaultType	fGblV15;
extern	DefaultType	fGblV17;
extern	DefaultType	fGblV18;
extern	DefaultType	fGblV19;
extern	DefaultType	fGblV20;
extern	DefaultType	fGblUDELAY1;

#define	PSM_VRefHiA		3.3		// ADC-A VREFHIA
#define	PSM_VRefHiB		3.3		// ADC-B VREFHIB
#define	PSM_VRefHiC		3.3		// ADC-C VREFHIC






DefaultType	fGblV16 = 0;
DefaultType	fGblV13 = 0;
DefaultType	fGblVma = 0;
DefaultType	fGblV6 = 0;
DefaultType	fGblV7 = 0;
DefaultType	fGblV8 = 0;
DefaultType	fGblV11 = 0;
DefaultType	fGblV12 = 0;
DefaultType	fGblV14 = 0;
DefaultType	fGblV15 = 0;
DefaultType	fGblV17 = 0;
DefaultType	fGblV18 = 0;
DefaultType	fGblV19 = 0;
DefaultType	fGblV20 = 0;
DefaultType	fGblUDELAY1 = 0;
interrupt void Task()
{
	DefaultType	fUDELAY1, fS4, fMULT3, fCOS1, fP3, fP1, fSUM3, fSUMP1, fP2;
	DefaultType	fFCNM1, fSUM2, fMULT6, fVgain2, fZOH3, fPSM_F28004x_ADC2_2;
	DefaultType	fMULT2, fSIN1, fSUMP2, fVTRI2, fS2, fLIM1, fS1, fSUM1, fMULT4;
	DefaultType	fVgain, fZOH1, fPSM_F28004x_ADC2, fVDC1, fMULT5, fVgain1, fZOH2;
	DefaultType	fPSM_F28004x_ADC2_1;

	ADC_CLR(2) = 1 << (1-1);	// Clear ADC interrupt flag 1
	CPU_PIEACK |= M__INT1;
	fUDELAY1 = fGblUDELAY1;


	fPSM_F28004x_ADC2_1 = ADC_RESULT(2, 1) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH2 = fPSM_F28004x_ADC2_1;
	fVgain1 = 33.571;
	fMULT5 = fZOH2 * fVgain1;
	fVDC1 = 50;
	fPSM_F28004x_ADC2 = ADC_RESULT(2, 0) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH1 = fPSM_F28004x_ADC2;
	fVgain = 33.571;
	fMULT4 = fZOH1 * fVgain;
	fSUM1 = fVDC1 - fMULT4;
	{	// 电压外环PI控制器 - 优化参数确保50V输出
		static DefaultType out_A = 0.0;
		// 优化参数: Kp=1.5, Ti=0.025s, 提高稳态精度和响应速度
		const DefaultType Kp = 1.5;
		const DefaultType Ti = 0.025;  // 积分时间常数
		const DefaultType Ki = Kp / Ti;  // Ki = 60
		fS1 = out_A + (Ki/20000.0) * fSUM1;  // 积分项
		fS1 = (fS1 < (-15)) ? (-15) : ((fS1 > 15.0) ? 15.0 : fS1);
		out_A = fS1;
		fS1 += Kp * fSUM1;  // 比例项
		fS1 = (fS1 < (-15)) ? (-15) : ((fS1 > 15.0) ? 15.0 : fS1);
	}
#ifdef	_DEBUG
	fGblV16 = fS1;
#endif

	// 电流参考限幅 - 调整为适合50V输出的范围
	fLIM1 = (fS1 > 30) ? 30 : ((fS1 < 0) ? 0 : fS1);
	{	// backward Euler
		static DefaultType out_A = 0.0;
		fS2 = out_A + (4.85/(0.04*20000)) * fUDELAY1;
		fS2 = (fS2 < (-10)) ? (-10) : ((fS2 > 10.0) ? 10.0 : fS2);
		out_A = fS2;
		fS2 += 4.85 * fUDELAY1;
		fS2 = (fS2 < (-10)) ? (-10) : ((fS2 > 10.0) ? 10.0 : fS2);
	}
#ifdef	_DEBUG
	fGblV13 = fS2;
#endif

	{
		static DefaultType wt = 1.0 - ((90) / 360.);
		static DefaultType dwt = (50) * 1.0 / 20000;
		static int32 delay = (int32)(0.002 * 1.0 * 20000);	// times of interrupt to start
		if (delay > 0) {
			delay--;
			fVTRI2 = 0.0;
		} else {
			if (wt > 0.4) {
				fVTRI2 = (1.0 - wt) * (2 / (1.0 - 0.4)) + (-1);
			} else {
				fVTRI2 = wt * (2 * 1.0 / 0.4) + (-1);
			}
			wt += dwt;
			if (wt >= 1.0)
				wt -= 1.0;
		}
	}
	fSUMP2 = fS2 + fVTRI2;
	fSIN1 = sin(fSUMP2 * (3.14159265 / 180.));
	fMULT2 = fLIM1 * fSIN1;
	fPSM_F28004x_ADC2_2 = ADC_RESULT(2, 2) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH3 = fPSM_F28004x_ADC2_2;
	fVgain2 = 1.0/0.264;
	fMULT6 = fZOH3 * fVgain2;
	fSUM2 = fMULT2 - fMULT6;
	fFCNM1 = 1.0/(0.01+1.0);
	// 电流内环控制器增益 - 优化为75以改善50V时的电流跟踪性能
	fP2 = fSUM2 * 75;
	fSUMP1 = fFCNM1 + fP2;
	fSUM3 = fMULT5 - fSUMP1;
	fP1 = fSUM3;
#ifdef	_DEBUG
	fGblVma = fP1;
#endif
#ifdef	_DEBUG
	fGblV6 = fLIM1;
#endif
	fP3 = fMULT5;
#ifdef	_DEBUG
	fGblV7 = fP3;
#endif
#ifdef	_DEBUG
	fGblV8 = fSIN1;
#endif
	fCOS1 = cos(fSUMP2 * (3.14159265 / 180.));
	fMULT3 = fP3 * fCOS1;
#ifdef	_DEBUG
	fGblV11 = fMULT3;
#endif
#ifdef	_DEBUG
	fGblV12 = fUDELAY1;
#endif
#ifdef	_DEBUG
	fGblV14 = fCOS1;
#endif
#ifdef	_DEBUG
	fGblV15 = fMULT2;
#endif
#ifdef	_DEBUG
	fGblV17 = fSUMP1;
#endif
#ifdef	_DEBUG
	fGblV18 = fSUMP2;
#endif
#ifdef	_DEBUG
	fGblV19 = fSUM1;
#endif
#ifdef	_DEBUG
	fGblV20 = fSUM2;
#endif
	{
		static	DefaultType fOutVal = 0.0;
		const DefaultType b0 = (1.0*1.0)/20.0E3/(1.0/(2*3.14159*10.0E3)+1.0/20.0E3);
		const DefaultType a1 = -1.0/(2*3.14159*10.0E3)/(1.0/(2*3.14159*10.0E3)+1.0/20.0E3);
		fS4 = b0 * fMULT3 - a1 * fOutVal;
		fOutVal = fS4;
	}
	fGblUDELAY1 = fS4;
	// Start of changing PWM1(1ph) registers
	// Set Duty Cycle
	{
		DefaultType _val = __fsat(fP1, 2 + 0, 0);
		_val = ((Uint32)(PWM_TBPRD(1))+1) * ((_val - 0) * (1.0 / 2));
		PWM_CMPA(1) = (int)_val;
	}
	// End of changing PWM1(1ph) registers
}


void Initialize(void)
{
	PS_SysInit(2, 20);
	PS_PwmStartStopClock(0);	// Stop Pwm Clock
	PS_TimerInit(0, 0);
	PS_SetVREF(0, 1, 0);	// Set external VRef for ADC-A
	PS_SetVREF(1, 1, 0);	// Set external VRef for ADC-B
	PS_SetVREF(2, 1, 0);	// Set external VRef for ADC-C

	{
	    int i, preAdcNo = -1;
	    /* PST_AdcAttr: Adc No., Channel No., Soc No., Trig Src, SampleTime(clock) */
	    const PST_AdcAttr aryAdcInit[3] = {
			{2, 0, 0, ADCTRIG_PWM1, 32},
			{2, 1, 1, ADCTRIG_PWM1, 32},
			{2, 2, 2, ADCTRIG_PWM1, 32}};
	    const PST_AdcAttr *p = aryAdcInit;
	    for (i = 0; i < 3; i++, p++) {
	        if (preAdcNo != p->nAdcNo) {
	            PS_AdcInit(p->nAdcNo);
	            preAdcNo = p->nAdcNo;
	        }
	        PS_AdcSetChn(p->nAdcNo, p->nChnNo, p->nSocNo, p->nTrigSrc, p->nWindSz);
	    }
	}

	PS_PwmInit(1, 0, 0, 1.e6/(20000*1.0), ePwmUseAB, ePwmStartHigh1, ePwmComplement, HRPWM_DISABLE);	// pwmNo, pinSel, waveType, period, outtype, PwmA, PWMB, UseHRPwm
	PS_PwmSetDeadBand(1, 0, 2, 3, 0, 1, 1);
	PS_PwmSetIntrType(1, ePwmIntrAdc, 1, 0);
	PS_AdcSetIntr(2, 1, 2, Task); // AdcNo, IntrNo, SocNo, Interrupt Vector
	PS_PwmSetTripAction(1, eTzHiZ, eTzHiZ);
	PWM_CMPA(1) = (0 - 0) / (1.0 * 2) * PWM_TBPRD(1);
	PSM_PwmStart(1);

	PS_PwmStartStopClock(1);	// Start Pwm Clock
}


void main()
{
	Initialize();
	PSM_EnableIntr();   // Enable Global interrupt INTM
	PSM_EnableDbgm();
	for (;;) {
	}
}

