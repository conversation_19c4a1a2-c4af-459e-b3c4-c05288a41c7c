/*
* PS_bios.h
*
*  Created on: 2018/06/08
*      Author: jiakai
*/

#ifndef PS_BIOS_H_
#define PS_BIOS_H_

#ifndef DSP28_DATA_TYPES
#define NULL    (0)
#define DSP28_DATA_TYPES
typedef int                int16;
typedef long               int32;
typedef long long          int64;
typedef unsigned int       Uint16;
typedef unsigned long      Uint32;
typedef unsigned long long Uint64;
typedef float              float32;
typedef long double        float64;
#endif

#define CPU1_NO    0       // CPU1 processor
#define CLA1_NO    1       // CLA1 processor

// F28x Cpu interrupt MASK
#define M__INT1  0x0001         // ADC A/B/C/D 1, XINT1/2, TINT0, IPC
#define M__INT2  0x0002         // Tripzone
#define M__INT3  0x0004         // PWM
#define M__INT4  0x0008         // Capture
#define M__INT5  0x0010         // eQep
#define M__INT6  0x0020         // SPI
#define M__INT7  0x0040         // DMA
#define M__INT8  0x0080         // SCI C/D
#define M__INT9  0x0100         // SCI A/B, CAN A/B, USB
#define M__INT10 0x0200         // ADC A/B/C/D 2/3/4
#define M__INT11 0x0400         // CLA
#define M__INT12 0x0800         // XINT3/4/5
#define M__INT13 0x1000         // TINT1
#define M__INT14 0x2000         // TINT2

extern cregister volatile unsigned int IFR;
extern cregister volatile unsigned int IER;

#define  PSM_EnableIntr()   asm(" clrc INTM")
#define  PSM_DisableIntr()  asm(" setc INTM")
#define  PSM_EnableDbgm()   asm(" clrc DBGM")
#define  PSM_DisableDbgm()  asm(" setc DBGM")
#define  PSM_EALLOW         asm(" EALLOW")
#define  PSM_EDIS           asm(" EDIS")

extern  const Uint16    PSK_SysClk; // CPu clock, unit: MHz

typedef float DefaultType;
typedef void (*PST_IntrVect)(void);
#define CPU_PIEACK  (*(Uint16*)0xCE1)

// CLA definitions
typedef enum
{
    CLA_TASK_1,  //!< CLA Task 1
    CLA_TASK_2,  //!< CLA Task 2
    CLA_TASK_3,  //!< CLA Task 3
    CLA_TASK_4,  //!< CLA Task 4
    CLA_TASK_5,  //!< CLA Task 5
    CLA_TASK_6,  //!< CLA Task 6
    CLA_TASK_7,  //!< CLA Task 7
    CLA_TASK_8   //!< CLA Task 8
} TClaTaskNo;

typedef enum
{
    CLA_TRIGGER_SOFTWARE    = 0U,   //!< CLA Task Trigger Source is Software

    CLA_TRIGGER_ADCA1       = 1U,   //!< CLA Task Trigger Source is ADCA1
    CLA_TRIGGER_ADCA2       = 2U,   //!< CLA Task Trigger Source is ADCA2
    CLA_TRIGGER_ADCA3       = 3U,   //!< CLA Task Trigger Source is ADCA3
    CLA_TRIGGER_ADCA4       = 4U,   //!< CLA Task Trigger Source is ADCA4
    CLA_TRIGGER_ADCAEVT     = 5U,   //!< CLA Task Trigger Source is ADCAEVT
    CLA_TRIGGER_ADCB1       = 6U,   //!< CLA Task Trigger Source is ADCB1
    CLA_TRIGGER_ADCB2       = 7U,   //!< CLA Task Trigger Source is ADCB2
    CLA_TRIGGER_ADCB3       = 8U,   //!< CLA Task Trigger Source is ADCB3
    CLA_TRIGGER_ADCB4       = 9U,   //!< CLA Task Trigger Source is ADCB4
    CLA_TRIGGER_ADCBEVT     = 10U,  //!< CLA Task Trigger Source is ADCBEVT
    CLA_TRIGGER_ADCC1       = 11U,  //!< CLA Task Trigger Source is ADCC1
    CLA_TRIGGER_ADCC2       = 12U,  //!< CLA Task Trigger Source is ADCC2
    CLA_TRIGGER_ADCC3       = 13U,  //!< CLA Task Trigger Source is ADCC3
    CLA_TRIGGER_ADCC4       = 14U,  //!< CLA Task Trigger Source is ADCC4
    CLA_TRIGGER_ADCCEVT     = 15U,  //!< CLA Task Trigger Source is ADCCEVT

    CLA_TRIGGER_XINT1       = 29U,  //!< CLA Task Trigger Source is XINT1
    CLA_TRIGGER_XINT2       = 30U,  //!< CLA Task Trigger Source is XINT2
    CLA_TRIGGER_XINT3       = 31U,  //!< CLA Task Trigger Source is XINT3
    CLA_TRIGGER_XINT4       = 32U,  //!< CLA Task Trigger Source is XINT4
    CLA_TRIGGER_XINT5       = 33U,  //!< CLA Task Trigger Source is XINT5

    CLA_TRIGGER_EPWM1INT    = 36U,  //!< CLA Task Trigger Source is EPWM1INT
    CLA_TRIGGER_EPWM2INT    = 37U,  //!< CLA Task Trigger Source is EPWM2INT
    CLA_TRIGGER_EPWM3INT    = 38U,  //!< CLA Task Trigger Source is EPWM3INT
    CLA_TRIGGER_EPWM4INT    = 39U,  //!< CLA Task Trigger Source is EPWM4INT
    CLA_TRIGGER_EPWM5INT    = 40U,  //!< CLA Task Trigger Source is EPWM5INT
    CLA_TRIGGER_EPWM6INT    = 41U,  //!< CLA Task Trigger Source is EPWM6INT
    CLA_TRIGGER_EPWM7INT    = 42U,  //!< CLA Task Trigger Source is EPWM7INT
    CLA_TRIGGER_EPWM8INT    = 43U,  //!< CLA Task Trigger Source is EPWM8INT


    CLA_TRIGGER_TINT0       = 68U,  //!< CLA Task Trigger Source is TINT0
    CLA_TRIGGER_TINT1       = 69U,  //!< CLA Task Trigger Source is TINT1
    CLA_TRIGGER_TINT2       = 70U,  //!< CLA Task Trigger Source is TINT2


    CLA_TRIGGER_ECAP1INT    = 75U,  //!< CLA Task Trigger Source is ECAP1INT
    CLA_TRIGGER_ECAP2INT    = 76U,  //!< CLA Task Trigger Source is ECAP2INT
    CLA_TRIGGER_ECAP3INT    = 77U,  //!< CLA Task Trigger Source is ECAP3INT
    CLA_TRIGGER_ECAP4INT    = 78U,  //!< CLA Task Trigger Source is ECAP4INT
    CLA_TRIGGER_ECAP5INT    = 79U,  //!< CLA Task Trigger Source is ECAP5INT
    CLA_TRIGGER_ECAP6INT    = 80U,  //!< CLA Task Trigger Source is ECAP6INT
    CLA_TRIGGER_ECAP7INT    = 81U,  //!< CLA Task Trigger Source is ECAP7INT

    CLA_TRIGGER_EQEP1INT    = 83U,  //!< CLA Task Trigger Source is EQEP1INT
    CLA_TRIGGER_EQEP2INT    = 84U,  //!< CLA Task Trigger Source is EQEP2INT

    CLA_TRIGGER_ECAP6INT2   = 92U,  //!< CLA Task Trigger Source is ECAP6INT2
    CLA_TRIGGER_ECAP7INT2   = 93U,  //!< CLA Task Trigger Source is ECAP7INT2

    CLA_TRIGGER_SDFM1INT    = 95U,  //!< CLA Task Trigger Source is SDFM1INT
    CLA_TRIGGER_SDFM1DRINT1 = 96U,  //!< CLA Task Trigger Srce is SDFM1DRINT1
    CLA_TRIGGER_SDFM1DRINT2 = 97U,  //!< CLA Task Trigger Srce is SDFM1DRINT2
    CLA_TRIGGER_SDFM1DRINT3 = 98U,  //!< CLA Task Trigger Srce is SDFM1DRINT3
    CLA_TRIGGER_SDFM1DRINT4 = 99U,  //!< CLA Task Trigger Srce is SDFM1DRINT4


    CLA_TRIGGER_PMBUSAINT   = 105U, //!< CLA Task Trigger Source is PMBUSAINT


    CLA_TRIGGER_SPITXAINT   = 109U, //!< CLA Task Trigger Source is SPITXAINT
    CLA_TRIGGER_SPIRXAINT   = 110U, //!< CLA Task Trigger Source is SPIRXAINT
    CLA_TRIGGER_SPITXBINT   = 111U, //!< CLA Task Trigger Source is SPITXBINT
    CLA_TRIGGER_SPIRXBINT   = 112U, //!< CLA Task Trigger Source is SPIRXBINT

    CLA_TRIGGER_LINAINT1    = 117U, //!< CLA Task Trigger Source is LINAINT1
    CLA_TRIGGER_LINAINT0    = 118U, //!< CLA Task Trigger Source is LINAINT0


    CLA_TRIGGER_CLA1PROMCRC = 121U, //!< CLA Task Trigger Srce is CLA1PROMCRC

    CLA_TRIGGER_FSITXAINT1  = 123U, //!< CLA Task Trigger Source is FSITXAINT1
    CLA_TRIGGER_FSITXAINT2  = 124U, //!< CLA Task Trigger Source is FSITXAINT2
    CLA_TRIGGER_FSIRXAINT1  = 125U, //!< CLA Task Trigger Source is FSIRXAINT1
    CLA_TRIGGER_FSIRXAINT2  = 126U, //!< CLA Task Trigger Source is FSIRXAINT2

    CLA_TRIGGER_CLB1INT      = 127, //!< CLA Task Trigger Source is CLB1INT
    CLA_TRIGGER_CLB2INT      = 128, //!< CLA Task Trigger Source is CLB2INT
    CLA_TRIGGER_CLB3INT      = 129, //!< CLA Task Trigger Source is CLB3INT
    CLA_TRIGGER_CLB4INT      = 130, //!< CLA Task Trigger Source is CLB4INT

} TClaTrig;

#define CLA_NOTASK      0
#define CLA_TASK1_BIT    1
#define CLA_TASK2_BIT    2
#define CLA_TASK3_BIT    4
#define CLA_TASK4_BIT    8
#define CLA_TASK5_BIT    16
#define CLA_TASK6_BIT    32
#define CLA_TASK7_BIT    64
#define CLA_TASK8_BIT    128

inline void PSM_ClaForceTask(int16 taskNo)
{
    PSM_EALLOW;
    *((Uint16 *)0x1422) |= 1 << taskNo;
    PSM_EDIS;
}

void PS_ClaInit(int16 claDataSz, int16 claProgSz, Uint32 softTaskBit);
void PS_ClaSetIntr(TClaTaskNo taskNo, TClaTrig trigger);
void PS_PwmSetClaIntr(int16 pwmNo, TClaTaskNo taskNo);
void PS_AdcSetClaIntr(int16 adcNo, int16 intrNo, int16 socNo, TClaTaskNo taskNo);

// Encoder: encoderNo=0-->Encoder1, encoderNo=1-->Encoder2
#define EQEP_QCLR(encoderNo) *((Uint16*)(0x511A + encoderNo * 0x40))
#define EQEP_QPOSCNT(encoderNo) *((Uint32*)(0x5100 + encoderNo * 0x40))
#define EQEP_QFLG(encoderNo) *((Uint16*)(0x5119 + encoderNo * 0x40))
void PS_EncInit(Uint16 nQepA, Uint16 nQepB, int16 nQepI, int16 nQepS, Uint16 bReverse, Uint16 bZChgPolarity, Uint16 bSChgPolarity, Uint32 nResolution);
void PS_EncSetIntrVector(Uint16 encoderNo, Uint16 bIndex, Uint16 bStrobe, Uint16 trigEdge, PST_IntrVect vec);
void PS_EncEnableIntr(Uint16 encoderNo, Uint16 bEnable, Uint16 bIndex, Uint16 bStrobe);
void PS_EncSetClaIntr(Uint16 encoderNo, Uint16 bIndex, Uint16 bStrobe, Uint16 trigEdge, TClaTaskNo taskNo);
void PS_EncInitIndexStrobeLatch(Uint16 encoderNo, Uint16 nIndexStrobeType);
Uint32 PS_EncCurIndexPos(Uint16 encoderNo, Uint16 resetFlag);
Uint32 PS_EncFirstIndexPos(Uint16 encoderNo, Uint16 resetFlag);
Uint32 PS_EncCurStrobePos(Uint16 encoderNo, Uint16 resetFlag);
Uint32 PS_EncFirstStrobePos(Uint16 encoderNo, Uint16 resetFlag);

inline void PSM_EncEntryIntr(Uint16 encoderNo)
{
    IER &= (M__INT5 - 1);
    EQEP_QCLR(encoderNo) = 0xffff;
    PSM_EnableIntr();
    CPU_PIEACK = M__INT5;
}
inline Uint32 PSM_EncGetCount(Uint16 encoderNo)
{
    return EQEP_QPOSCNT(encoderNo);
}

inline Uint16 PSM_EncIsIntrStrobe(Uint16 encoderNo)
{
    return EQEP_QFLG(encoderNo) & 0x200;
}

inline Uint16 PSM_EncIsIntrIndex(Uint16 encoderNo)
{
    return EQEP_QFLG(encoderNo) & 0x400;
}

// Counter (use same device as encoder)
void PS_CntInit(Uint16 nQepA, Uint16 nQepB, int16 nQepI, int16 nQepS);

// Capture
#define CAPINTR_EVT1        2
#define CAPINTR_EVT2        4
#define CAPINTR_PRD         0x80
#define ECAP_ECCTL1(capNo)  ((*(Uint16*)(0x5214 - 0x40 + 0x40 * capNo)) & 1)    // ECCTL1.CAP1POL
#define	ECAP_CAP1(capNo)	(*(Uint32*)(0x5204 - 0x40 + 0x40 * capNo))
#define	ECAP_CAP2(capNo)	(*(Uint32*)(0x5206 - 0x40 + 0x40 * capNo))
#define	ECAP_EVT1(capNo)	((*(Uint16*)(0x5217 - 0x40 + 0x40 * capNo)) & CAPINTR_EVT1)
#define	ECAP_EVT2(capNo)	((*(Uint16*)(0x5217 - 0x40 + 0x40 * capNo)) & CAPINTR_EVT2)
#define	ECAP_ECCLR(capNo)	(*(Uint16*)(0x5218 - 0x40 + 0x40 * capNo))
#define	ECAP_CTRPHS(capNo)	(*(Uint32*)(0x5202 - 0x40 + 0x40 * capNo))
#define ECAP_ECEINT(capNo)  ((*(Uint16*)(0x5216 - 0x40 + 0x40 * capNo)) & (CAPINTR_EVT1 | CAPINTR_EVT2))
Uint16 PS_CapInit(Uint16 inputMuxSelect, Uint16 capNo, Uint16 evtFilter, Uint16 askDelta);
void PS_CapSetIntrVector(Uint16 capNo, Uint16 intrFlag, PST_IntrVect vec);
void PS_CapSetClaIntr(Uint16 capNo, Uint16 intrFlag, TClaTaskNo taskNo);

inline Uint32 PSM_CapIsRisingEdge(Uint16 capNo)
{
    return (ECAP_ECEINT(capNo) == (CAPINTR_EVT1 | CAPINTR_EVT2)) ? (ECAP_EVT1(capNo) >> 1) : !ECAP_ECCTL1(capNo);
}

inline Uint32 PSM_CapGetCurCount(Uint16 capNo)
{
    return ECAP_EVT1(capNo) ? ECAP_CAP1(capNo) : ECAP_CAP2(capNo);
}

inline void PSM_CapEntryIntr()
{
    IER &= (M__INT4 - 1);
    PSM_EnableIntr();
}

inline void PSM_CapExitIntr(Uint16 capNo)
{
    ECAP_ECCLR(capNo) = 0xff;
    CPU_PIEACK = M__INT4;
}

void PS_CapPwmInit(Uint16 pwmNo, float period, Uint16 stopStatus, Uint16 isStartLow);
void PS_CapPwmPhaseInit(Uint16 pwmNo, int16 startPwmNo, float phaseDelay);
void PS_CapPwmTrigSync(Uint16 pwmNo);
void PS_CapPwmRun(Uint16 pwmNo);
void PS_CapPwmSetRate(Uint16 pwmNo, float rate);
void PS_CapPwmStart(Uint16 pwmNo);
void PS_CapPwmStop(Uint16 pwmNo);


inline void PSM_CapPwmSetPhase(Uint16 pwmNo, float phaseShifted)
{
    ECAP_CTRPHS(pwmNo) = (Uint32)(__fracf32(__fracf32(phaseShifted ) + 1.0) * ECAP_CAP1(pwmNo));
}

void PS_TimerInit(int timerNo, Uint32 interval);
void PS_TimerSetIntrVector(int timerNo, PST_IntrVect vec);

inline void PSM_Timer1IntrEntry()
{
    IER &= M__INT13 - 1;
    asm(" nop");
    PSM_EnableIntr();
}

inline void PSM_Timer2IntrEntry()
{
    IER &= M__INT14 - 1;
    asm(" nop");
    PSM_EnableIntr();
}

Uint32 PS_GetSysTimer(void);
Uint64 PS_GetBigTime(void);
void PS_DelayUs(Uint32 interval);

typedef enum
{
    eOtherFunc,
    eGpioIn,
    eGpioInPullup,
    eGpioInInv,
    eGpioInInvPullup,
    eGpioOut,
    eGpioOutPullup,
    eGpioOpenDrain
} PST_GpioType;

typedef enum
{
    eSync1Samp=0,
    eSync3Samp,
    eSync6Samp,
    eASync
} PST_GpioSync;

#define PSM_GpioData(grpNo) *((Uint32*)(0x7F00 + 8 * (grpNo)))	// For AIOs grpNo is 7
#define PSM_GpioSet(grpNo) *((Uint32*)(0x7F02 + 8 * (grpNo)))
#define PSM_GpioClear(grpNo) *((Uint32*)(0x7F04 + 8 * (grpNo)))

inline void PSM_GpioSetOutput(int16 pinNo, int16 val)
{
    if (val != 0) {
        PSM_GpioSet(pinNo>>5) = 1UL << (pinNo&0x001F);
    } else {
        PSM_GpioClear(pinNo>>5) = 1UL << (pinNo&0x001F);
    }
}

inline Uint16 PSM_GpioGetInput(int16 pinNo)
{
    return (PSM_GpioData(pinNo>>5)&(1UL<<(pinNo&0x001F))) ? 1 : 0;
}

void PS_GpioSetFunc(int16 pinNo, int16 funcNo, PST_GpioSync sync, PST_GpioType gpioType, int16 cpu);
void PS_GpioSetSampTime(Uint16 nSigGrp, Uint32 nSampleTime);
void PS_AioSetFunc(int16 pinNo, PST_GpioSync sync, Uint16 aioInv);
void PS_AioSetSampTime(int16 pinNo, Uint32 nSampleTime);
Uint16 PS_SetExtIntrVector(Uint16 intrNo, int trigerEdge, PST_IntrVect vec);
inline Uint16 PSM_DigitInGetValue(int16 chnNo)
{
    return PSM_GpioData(chnNo >> 5) & (1UL << (chnNo & 0x001F));
}

void PS_DigitInSetSampleTime(Uint16 nSigGrp, Uint32 nSampleTime);
void PS_ExitExtIntrFirst2(void);
void PS_ExitExtIntrOther(void);

/// CPT
#define PSM_PieCtrlRegs_PIEACK *((Uint32*)(0x0CE1))

inline void PS_ExtIntrHighEntry(void)
{
    PSM_PieCtrlRegs_PIEACK = M__INT1;
}

inline void PS_ExtIntrLowEntry(void)
{
    IER &= (M__INT12 - 1);
    PSM_PieCtrlRegs_PIEACK = M__INT12;
    PSM_EnableIntr();
}

inline void PSM_DigitOutSetValue(int16 chnNo, DefaultType value)
{
    Uint32 *pReg = (Uint32*)(8 * (chnNo >> 5) + ((value > 0.3) ? 0x7F02 : 0x7F04));
    *pReg |= (Uint32)1 << (chnNo & 31);
}

// Sci communication
typedef union {
    float       dataFloat;
    Uint32      dataInt32;
} PST_Data;

typedef union {
    Uint16      all;
    struct {
    Uint16      nCount:8;
    Uint16      nSeqNo:8;
    } bit;
} PST_SeqNo;

typedef struct {
    PST_SeqNo   nSeqNo;
    PST_Data    data;
} PST_BufItem;

typedef void (*PST_ProcCmd)(PST_BufItem* pItem);

void PS_SciInit(Uint16 nRxGpioNo, Uint16 nTxGpioNo, Uint32 speed, int16 parityBit,
PST_BufItem* pTxBuf, int szBuf, PST_ProcCmd	procItem);
void PS_SciSendInitStr(char* sInitStr);
int PS_SciSendItem(PST_BufItem* pItem);
void PS_SciClearSendBuf(void);
int PS_IsTxQueueEmpty(void);

typedef void (*CallSetChipSel)(void);
typedef void (*CallBackCmd)(void);
typedef void (*CallBackSync)(Uint16);
typedef struct {
    Uint16      nLenCmd:5;
    Uint16      nReserve:8;
    Uint16      bIsInput:1;     // 1: Spi Input Element, 0: Spi Output
    Uint16      bPostRecv:1;    // 1: call post-receving function, 0: call schematic function
    Uint16      bDevSync:1;     // if call hardware synchronize function
} PST_SpiAttr;

typedef union {
    void*           pVoid;
    CallBackCmd     pBack;
    CallBackSync    pPost;
} PST_SpiFunc;

typedef struct {
    Uint16          nBitShift;  // bits need to shift left when put a word to SPI TX Buf
    // it equals to 16 - Word Length
    Uint16          nClkFreq;       // SPI clock frequency
    Uint16          nClkCfg;        // clock config,
    Uint16          nDelayCmd;      // dely in 2 commands, in ns
    Uint16*         pArySpiBbr;
    CallSetChipSel  pCallSetChipSel;// the address of chip select function
    CallBackSync    pArySyncOut;    // this is only called when data received.
} PST_SpiDev;

typedef struct {
    PST_SpiAttr objSpiSet;
    Uint16*     pArySendCmd;
    Uint16*     pAryRecvData;
    PST_SpiFunc CallbackRecv;   // should be NULL if discard receiving data
    CallBackCmd CallbackMaker;  // make a command or get result from receiving data
    Uint16      nSpiDev;        // Device Configuraton
} PST_SpiIo;

typedef union {
    float       fVal;
    int32       nVal;
} PST_SpiVal;

void PS_SpiInitBBR(const PST_SpiDev* pDev, int devSize);
void PS_SpiInit(const PST_SpiIo* spiIO, Uint16* pAryQueue, Uint16 lenQueue, Uint16 nSimoNo, Uint16 nSomiNo, Uint16 nClkNo, Uint16 nSteNo);
Uint16 PS_SpiPutQue(Uint16 index);
Uint16 PS_GetCurCmdIndex(void);
void PS_SpiTransmitCheck(void);


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// XBar_F28004x.c definitions

void PS_SysInit(int clkSrc, int extClk);
void PS_InXBarSetPin(int16 xbarNo, int16 gpioNo);
void PS_InXBarSetIntrVector(Uint16 intrNo, int trigerEdge, PST_IntrVect vec);
void PS_InXBarSetClaIntr(Uint16 intrNo, int trigerEdge, TClaTaskNo taskNo);
void PS_OutXBarSetSig(int16 xbarNo, int16 muxNo, int16 funcNo);
void PS_OutXBarSetFunc(int16 xbarNo, int16 bInv, int16 bLatch);
void PS_OutXBarClearLatch(int16 xbarNo);
void PS_OutXBarSetLatch(int16 xbarNo);
void PS_PwmXBarSetSig(int16 xbarNo, int16 muxNo, int16 funcNo);
void PS_PwmXBarInv(int16 xbarNo, int16 bInv);

inline void PSM_InXBarHighIntrEntry()
{
    CPU_PIEACK |= M__INT1;
}

inline void PSM_InXBarLowIntrEntry()
{
    IER &= M__INT12 - 1;
    CPU_PIEACK |= M__INT12;
    PSM_EnableIntr();
}

typedef enum
{
    ePwmDoNothingCmp,
    ePwmToggleZero,         // PWM output toggles when TBCTR=0
    ePwmTogglePRD,          // PWM output toggles when TBCTR=PRD
    ePwmToggleBoth,         // PWM output toggles when TBCTR=0 or TBCTR=PRD
    ePwmSetValleyResetPeak, // Set at valley/reset at peak
    ePwmResetValleySetPeak, // Reset at valley/set at peak
    ePwmSetValley,          // Set at Valley
    ePwmResetValley,        // Reset at Valley
    ePwmSetPeak,            // Set at Peak
    ePwmResetPeak,          // Reset at Peak
    ePwmComplement,         // PWMxB output is complementary with PWMxA
    ePwmStartLow1,          // PWMxA/B starts low and goes high when CMPA/B=TBCTR
    ePwmStartHigh1,         // PWMxA/B starts high and goes low when CMPA/B=TBCTR
    ePwmStartLow2,          // PWMxA starts low and goes high when CMPA=TBCTR goes low when CMPB=TBCTR
    ePwmStartHigh2,         // PWMxA starts high and goes low when CMPA=TBCTR goes high when CMPB=TBCTR
    ePwmComplementwithDB    // PWMxB output is complementary with PWMxA with DB
} PST_PwmCmpOut;

typedef enum
{
    ePwmEvtDoNothing,
    ePwmEvtLow,     // PWM goes low when event occurs
    ePwmEvtHigh,    // PWM output goes high when event occurs
    ePwmEvtToggle   // PWM output toggles when event occurs
} PST_PwmEvtOut;

typedef enum
{
    ePwmNoOutput,
    ePwmUseA,
    ePwmUseB,
    ePwmUseAB
} PST_PwmOutUse;

typedef enum
{
    eTzDisable,
    eTzEnOneShot,
    eTzEnCbc
} PST_PwmTzType;

typedef enum
{
    eTzEvtDisable,
    eTzEvtDCxHlow,
    eTzEvtDCxHhigh,
    eTzEvtDCxLlow,
    eTzEvtDCxLhigh,
    eTzEvtDCxHlowDCxLhigh
} PST_PwmDCEvtSel;

typedef enum
{
    eTDCAEvt1,
    eTDCAEvt2,
    eTDCBEvt1,
    eTDCBEvt2,
    eTTz1,
    eTTz2,
    eTTz3
} PST_PwmDCEvtT1T2;

typedef enum
{
    eTzHiZ = 0,
    eTzForceHigh,
    eTzForceLow,
    eTzDoNothing
} PST_PwmTzAction;

typedef enum
{
    eTzHiZ2 = 0,
    eTzForceHigh2,
    eTzForceLow2,
    eTzToggle,
    eTzDoNothing2 = 7
} PST_PwmTzAction2;

typedef enum
{
    ePwmNoAdc,
    ePwmTrigAdc,
    ePwmIntrAdc
} PST_PwmAdcAct;

#define HRPWM_DISABLE   0
#define HRPWM_NOCALIB   1
#define HRPWM_CALIB     2

#define PWM_TBPRD(_p)       *((Uint16 *)(0x3F63 + 0x100 * (_p)))
#define PWM_CMPA(_p)        *((Uint16 *)(0x3F6B + 0x100 * (_p)))
#define PWM_CMPAHR(_p)      *((Uint16 *)(0x3F6A + 0x100 * (_p)))
#define PWM_CMPB(_p)        *((Uint16 *)(0x3F6D + 0x100 * (_p)))
#define PWM_CMPBHR(_p)      *((Uint16 *)(0x3F6C + 0x100 * (_p)))
#define PWM_TBPHS(_p)       *((Uint16 *)(0x3F61 + 0x100 * (_p)))
#define PWM_TBCTL(_p)       *((Uint16 *)(0x3F00 + 0x100 * (_p)))
#define PWM_TBCTL2(_p)      *((Uint16 *)(0x3F01 + 0x100 * (_p)))
#define PWM_TZCLR(_p)       *((Uint16 *)(0x3F97 + 0x100 * (_p)))
#define PWM_TZFRC(_p)       *((Uint16 *)(0x3F9B + 0x100 * (_p)))
#define PWM_TZCBCCLR(_p)    *((Uint16 *)(0x3F98 + 0x100 * (_p)))
#define PWM_TZOSTCLR(_p)    *((Uint16 *)(0x3F98 + 0x100 * (_p)))
#define PWM_TZFLG(_p)       *((Uint16 *)(0x3F93 + 0x100 * (_p)))
#define PWM_ETCLR(_p)       *((Uint16 *)(0x3FAA + 0x100 * (_p)))

int16 PS_PwmInit(int16 pwmNo, int16 pwmSeqNo, int16 carrierType, DefaultType period,
PST_PwmOutUse outUse, PST_PwmCmpOut cmpOutA, PST_PwmCmpOut cmpOutB, int16 bHRPwm);
void PS_PwmSetPhaseDelay(int16 pwmNo, int16 startPwmNo, DefaultType delay);
void PSM_PwmSetRate(int16 pwmNo, DefaultType rate);
void PSM_Pwm2phSetRate(int16 pwmNo, DefaultType rate1, DefaultType rate2);
void PS_PwmT1T2Source(int16 pwmNo, PST_PwmDCEvtT1T2 srcT1, PST_PwmDCEvtT1T2 srcT2);
//void PS_PwmEventAction(int16 pwmNo, PST_PwmEvtOut pwmxAOut, PST_PwmEvtOut pwmxBOut);
void PS_PwmEventAction(int16 pwmNo, int16 srcTno, PST_PwmDCEvtT1T2 evtSrc,
PST_PwmEvtOut pwmOutUA, PST_PwmEvtOut pwmOutUB,
PST_PwmEvtOut pwmOutDA, PST_PwmEvtOut pwmOutDB);
int16 PS_Pwm3phInit(int16 pwmGrp, int16 pwmSeqNo, int16 carrierType, DefaultType period,
PST_PwmCmpOut cmpOutA, PST_PwmCmpOut cmpOutB, int16 bHRPwm);
void PSM_Pwm3phSetRate(int16 pwmGrp, DefaultType uRate, DefaultType vRate, DefaultType wRate);
void PS_PwmSetTripAction(int16 pwmNo, PST_PwmTzAction pwmA, PST_PwmTzAction pwmB);
void PS_PwmSetNewTripAction(int16 pwmNo, int16 cbcLockClear, PST_PwmTzAction2 tzau, PST_PwmTzAction2 tzad, PST_PwmTzAction2 tzbu, PST_PwmTzAction2 tzbd);
void PS_PwmSetIntrType(int16 pwmNo, int16 trigAdc, int16 scalingFactor, DefaultType trigPos);
void PS_PwmSetIntrVector(int16 pwmNo, PST_IntrVect vec);
//void PS_PwmSetIntrVector(int16 pwmNo, int16 trigAdc, int16 adcSocNo, int16 selAdc, PST_IntrVect vec);
inline void PSM_PwmIntrEntry(int pwmNo)
{
    //  assert((pwmNo > 0) && (pwmNo <= 12));

    IER &= M__INT3 - 1; // M__INT3 is defined in PS_bios.h
    PWM_ETCLR(pwmNo) |= 1; // PwmReg.ETCLR.bit.INT = 1;
    PSM_EnableIntr();
    CPU_PIEACK |= M__INT3;
}
inline Uint16 PSM_PwmIsOneShotTz(int16 pwmNo)
{
    return PWM_TZFLG(pwmNo) & 0x2c;
}
void PS_PwmSetDeadBand(int16 pwmNo, int16 inMode, int16 polSel, int16 outMode, int16 swapAB, float dbRed, float dbFed);
void PS_PwmSetTripZone(int16 pwmNo, int tzNo, PST_PwmTzType tzType);
void PS_PwmSetTzVector(int16 pwmNo, PST_IntrVect vec);
inline void PSM_PwmTzIntrEntry(int16 pwmNo)
{
    IER &= M__INT2 - 1; // M__INT2 is defined in PS_bios.h
    PSM_EnableIntr();
    CPU_PIEACK |= M__INT2;
}

inline void PSM_PwmTzCbcReset(int16 pwmNo)
{
    PSM_EALLOW;
    PWM_TZCLR(pwmNo) = 3; // PwmReg.TZCLR.bit.CBC = 1; PwmReg.TZCLR.bit.INT = 1;
    PSM_EDIS;
}

void PS_PwmSetDcEvent(int16 pwmNo, int dcEvtNo, int16 dcHSrcNo, int16 dcLSrcNo, PST_PwmDCEvtSel dcEvtSel);
void PS_PwmSetDcEvents(int16 pwmNo, int dcEvtNo, Uint16 dcHSrcBit, Uint16 dcLSrcBit, PST_PwmDCEvtSel dcEvtSel);
void PS_PwmSetDcTrip(int16 pwmNo, Uint16 evtSet);
void PS_PwmSetDcEvtFilter(int16 pwmNo, int16 dcEvtNo, int16 dcFltApply, int16 winStartMode,
int16 nInvWin, DefaultType winOffset, DefaultType winWidth);
void PS_PwmSetDcTripAction(int pwmNo, int16 dcEvtNo, PST_PwmTzAction dcAction);
void PS_PwmSetDcSoc(int pwmNo, int16 dcEvtNo);
void PS_PwmStartStopClock(int16 nStart);
void PS_PwmSetGlobalLoad(int pwmNo, int16 loadMode, int16 regs, int16 times);
void PS_PwmReloadOneShot(int pwmNo);
void PS_PwmValleySwitch(int16 pwmNo, int16 dcEvtNo, int16 edgeStartEvent, int16 edgeDelayApply, int16 edgeApplyMode);
void PS_PwmValleySwitchDelay(int16 pwmNo, int16 startEdge, int16 stopEdge, DefaultType softDelay, int16 delayType);
void PS_PwmStartStopClock(int16 bStart);


inline void PSM_PwmStop(int16 pwmNo)
{
    PSM_EALLOW;
    PWM_TZFRC(pwmNo) = 4;
    PSM_EDIS;
}

inline void PSM_Pwm3phStop(int16 pwmNo)
{
    PSM_EALLOW;
    PWM_TZFRC(pwmNo * 3 - 2) = 4;
    PWM_TZFRC(pwmNo * 3 - 1) = 4;
    PWM_TZFRC(pwmNo * 3) = 4;
    PSM_EDIS;
}

inline void PSM_PwmStart(int16 pwmNo)
{
    PSM_EALLOW;
    PWM_TZCLR(pwmNo) = (PWM_TZCLR(pwmNo) & 0xC000) | 4;
    PSM_EDIS;
}

inline void PSM_Pwm3phStart(int16 pwmNo)
{
    PSM_EALLOW;
    PWM_TZCLR(pwmNo * 3 - 2) = PWM_TZCLR(pwmNo * 3 - 1) = PWM_TZCLR(pwmNo * 3) = (PWM_TZCLR(pwmNo * 3 - 2) & 0xC000) | 4;
    PSM_EDIS;
}


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// Adc_F28004x.c definitions

typedef struct
{
    int16   nAdcNo;
    int16   nChnNo;
    int16   nSocNo;
    int16   nTrigSrc;
    int16   nWindSz;
} PST_AdcAttr;

#define ADCTRIG_TIMER1  2
#define ADCTRIG_TIMER2  3
#define ADCTRIG_PWM1    5
#define ADCTRIG_PWM2    7
#define ADCTRIG_PWM3    9
#define ADCTRIG_PWM4    11
#define ADCTRIG_PWM5    13
#define ADCTRIG_PWM6    15
#define ADCTRIG_PWM7    17
#define ADCTRIG_PWM8    19
#define ADCTRIG_PWM9    21
#define ADCTRIG_PWM10   23
#define ADCTRIG_PWM11   25
#define ADCTRIG_PWM12   27
#define ADCTRIG_CPU2TM0 29
#define ADCTRIG_CPU2TM1 30
#define ADCTRIG_CPU2TM2 31

void PSM_AdcEnable(int16 adcNo);
void PS_AdcInit(int16 adcNo);
void PS_AdcSetChn(int16 adcNo, int16 chnNo, int16 socNo, int16 trigSrc,
int16 winSz);
void PS_AdcSetBurstMode(int16 adcNo, int16 burstSize, int16 trigSrc, int16 numSoc);
void PS_AdcSetBurstChn(int16 adcNo, int16 chnNo, int16 socNo, DefaultType winSz);
void PS_AdcSetIntr(int16 adcNo, int16 intrNo, int16 socNo, PST_IntrVect vec);
Uint16 PSM_AdcGetValue(int16 adcNo, int16 socNo);
void PSM_AdcIntrEntry(int16 adcNo, int16 intrNo);
void PS_SetVREF(int module, int mode, int ref);

#define ADC_CLR(_a) *((Uint16 *)(0x7404 + 0x80 * _a))

#define ADC_RESULT(_adc,_soc)   *((Uint16 *)(0x0B00 + 0x20*_adc + _soc))


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// Comp_F28004x.c definitions
#define CMP_RAMPMAXREF(_ch)     *((Uint16 *)(0x5C6A + 0x20 * _ch))
#define CMP_RAMPDECVAL(_ch)     *((Uint16 *)(0x5C6E + 0x20 * _ch))
#define CMP_DACH(_ch)           *((Uint16 *)(0x5C66 + 0x20 * _ch))
#define CMP_DACL(_ch)           *((Uint16 *)(0x5C72 + 0x20 * _ch))

void PS_CompInit(int16 compNo, int16 inHyst, int16 pwmSync, float pwmSyncPos, int16 blankpwmSrc);
void PS_CompSideInit(int16 compNo, int16 bLow, int16 negUse, int16 outInv, int16 syncType);
void PS_CompSetDigitFilter(int16 compNo, int16 bLow, float sampWin, float thresh);
void PS_CompUseDac(int16 compNo, int16 vRef);
void PSM_CompDacSetValue(int16 compNo, int16 bLow, int32 dacVolt);
int16 PSM_CompGetValue(int16 compNo, int16 bLow);
void PS_CompRampInit(int16 compNo, int16 delayTime, int32 decVolt);
void PSM_CompRampSetValue(int16 compNo, int32 nVal);
inline void PSM_CompDacHSetValue(int16 compNo, int32 dacVolt)
{
    CMP_DACH(compNo) = __IQsat(dacVolt, 4095, 0);
}
inline void PSM_CompDacLSetValue(int16 compNo, int32 dacVolt)
{
    CMP_DACL(compNo) = __IQsat(dacVolt, 4095, 0);
}


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// Dac_F28004x.c definitions

void PS_DacInit(int16 dacNo, int16 pwmSync);
void PS_DacSetVRef(int16 dacNo, int16 gainMode, int16 vRef);
void PS_DacSetValue(int16 dacNo, int32 val);

#define DACVALS(dacNo) *((Uint16*)(0x5c03 - 0x10 + dacNo * 0x10))
inline void PSM_DacSetValue(int dacNo, int32 val)
{
    DACVALS(dacNo) = __IQsat(val, 4095, 0);
}


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// AsysIntrcnct_F28004x.c definitions

void PS_AsysIntrcnctCnfg(int16 cmpssNo, int16 inMux, Uint32 value);

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// PGA_F28004x.c definitions
#define PGA_CTLREG(pgaNo) *((Uint32*)(0x5B0F - 0x10 + pgaNo * 0x10))
#define PGA_SETGAIN(pgaNo, val) {PGA_CTLREG(pgaNo) &= ~((Uint32)0xe0);PGA_CTLREG(pgaNo) |= __IQsat((int32)val, 3, 0) << 5;}
void PS_PgaInit(int16 pgaNo, int16 pgaGain, int16 pgaFiltRes);
void PS_PgaDisable(int16 pgaNo);


// F28004x SDFM
#define ERRFLG_SDFM_IEL     1       // Allow IEL interrupt
#define ERRFLG_SDFM_IEH     2       // Allow IEH interrupt
typedef void (*PST_SdfmErrVec)(Uint32 errFlag);
void PS_SdfmConfigDataFilterFIFO(Uint16 filterNumber, Uint16 filterType, Uint16 mode, Uint16 DOSR, Uint16 b32bitb, Uint16 shift_bits, Uint16 fifoLevel);
void PS_SdfmConfigComparator(Uint16 filterNumber, Uint16 filterType, Uint16 COSR, int cmpType, Uint16 HLT, Uint16 LLT, Uint16 HLTZ);
void PS_SdfmDisableComparator(Uint16 filterNumber);
void PS_SdfmSetErrVec(Uint16 filterNumber, Uint16 errFlag, PST_SdfmErrVec vec);
void PS_SdfmSetSyncPwm(int16 pwmNo, int16 syncSocA, DefaultType syncPosA, int16 syncSocB, DefaultType syncPosB);
void PS_SdfmSetFifoIntr(Uint16 filterNumber, PST_IntrVect vec);
void PS_SdfmEnable();
#define	PSM_SDFM_FifoData(FiltNo) *((int32*)((0x5E18 - 0x10) + 0x10 * FiltNo))
inline void PSM_SDFM_FifoData_Discard(int16 FiltNo) {int32 val = PSM_SDFM_FifoData(FiltNo);}
#define PSM_SDFM_Data16(FiltNo) *((int16*)((0x5E17 - 0x10) + 0x10 * FiltNo))
#define PSM_SDFM_Data32(FiltNo) *((int32*)((0x5E16 - 0x10) + 0x10 * FiltNo))
#define	PSM_SDFM_SDIFlag()	*((Uint32*)0x5E00)
inline void PSM_SDFM_FifoFlagClr(Uint32 flgClr) {
    *((Uint32*)0x5E02) = flgClr;
}
inline int PSM_SDFM_FifoIntrReady(Uint32 FiltBits) {
    FiltBits = (FiltBits & 15) << 20;
    return ((FiltBits & PSM_SDFM_SDIFlag()) == FiltBits) ? 1 : 0;
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// eCan_F28004x.c definitions
// Can messageBox type
#define CAN_MSG_INPUT_TYPE          1   // CAN_MSG_OBJ_TYPE_RX
#define CAN_MSG_OUTPUT_TYPE         2   // CAN_MSG_OBJ_TYPE_TX
#define CAN_MSG_REMOTEOUT_TYPE      3   // CAN_MSG_OBJ_TYPE_TX_REMOTE
#define CAN_MSG_REMOTEINOUT_TYPE    4   // CAN_MSG_OBJ_TYPE_RXTX_REMOTE

struct PST_eCanMsgID_Bits
{        // bits  description
    Uint32 MsgID:29;    // 0:28
    Uint32 Dir:1;       // 29
    Uint32 Xtd:1;       // 30
    Uint32 MsgVal:1;    // 31
};

union PST_CanMsgID_Reg
{
    struct PST_eCanMsgID_Bits ebit;
    Uint32  all;
};

struct PST_CanMsgCtrl_Bits
{     // bits  description
    Uint32 DLC:4;          // 0:3 data length
    Uint32 MsgNo:5;        // 4:8 message ID
    Uint32 MsgType:3;      // 9:11 message type: CAN_MSG_INPUT_TYPE, CAN_MSG_OUTPUT_TYPE, CAN_MSG_REMOTEOUT_TYPE and CAN_MSG_REMOTEINOUT_TYPE
    Uint32 rsvd:20;        // 12:31 reserved
};

union PST_CanMsgCtrl_Reg
{
    struct PST_CanMsgCtrl_Bits bit;
    Uint32 all;
};

union PST_CanMD_Reg
{
    Uint32 MData[2];
    Uint64 MData64;
};

typedef struct
{
    union PST_CanMsgID_Reg MSGID;
    union PST_CanMsgCtrl_Reg MSGCTRL;
    union PST_CanMD_Reg MD;
} PST_MBox;

typedef union {
    float fVal;
    int32 nVal;
} PST_Var;

typedef void PST_ProcInCanMsg(PST_MBox* pBox);  //unpack function, provided by PSIM

typedef void PST_ProcCanErr(Uint32 nErr);   // user defined error process function.

typedef struct
{
    Uint16 nSrcNo;      // 0: CANA, 1: CANB
    Uint32 nCanSpeed;
    Uint32 nErrMask;        // 1 means enable the error interrupt
    PST_ProcInCanMsg* funCanMsg;
    PST_ProcCanErr* funCanErr;
} PST_CanCfg;

// PST_CanCfg.nErrMask can be set any the following error flags:
#define PSM_DCAN_ERR        (1) // parity, bus off and EWarn errors generate interrupt
#define PSM_DCAN_STATUS     (2) // TxOk, RxOk and LEC flags generate interrupt

typedef struct
{
    Uint16 nStartMB;  // Start position of input mailbox for this mask
    Uint16 nNumMB;   // the number of input mailbox with same maskID
    Uint32 nMask;   // local mask
    Uint32 nMaskID;  // maskID
    Uint16 nRemoteSrc;  // mailbox transmits remote frame data on request
    Uint16 nRemoteDest;  // mailbox receives remote frame data
    Uint16 nExtension; // use extended message IDs
} PST_CanInCfg;

void PS_CanInit(Uint16 rxPin, Uint16 txPin, PST_CanCfg* pCanData, int16 tSeg1, int16 tSeg2);
void PS_CanInMailBoxInit(PST_CanInCfg* pCanInCfg);
void PS_CanOutMailBoxInit(PST_MBox* pBox);
int16 PS_CanTx(PST_MBox* pBox);
int16 PS_CanDataTransform(PST_MBox* pBox);
int16 PS_CanRemData(PST_MBox* pBox);
int16 PS_CanRemReq(PST_MBox* pBox);

interrupt void PS_CanRxInterrupt(void);
interrupt void PS_CanErrInterrupt(void);

// F28004x CpuTimer counter(32bit), tmNo: 0-2
#define CPU_TMPRD(tmNo)		(*(Uint32*)(0x0C02 + 8 * (tmNo)))
#define CPU_TMCNT(tmNo)		(*(Uint32*)(0x0C00 + 8 * (tmNo)))
#endif /* PS_BIOS_H_ */

